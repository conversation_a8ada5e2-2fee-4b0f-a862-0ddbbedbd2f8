#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Log file processing script
Executes daily at 2 AM to copy log files from specified directories and rename them
"""

import os
import shutil
import logging
from datetime import datetime, timedelta
from pathlib import Path

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('log_processor.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)

# Configuration parameters
BASE_DIRS = [
    "/home/<USER>/psis_console_log/log_mount_137",
    "/home/<USER>/psis_console_log/log_mount_196"
]

# Target directory - all copied files will be placed here
TARGET_DIR = "/home/<USER>/psis_console_log/log_mount"

# File name mapping
FILE_MAPPING = {
    "162.11_P5": "100.120.162.113_slot11",
    "162.12_P9": "100.120.162.113_slot12",
    "162.19_P1": "100.120.162.106_slot11"
}

def get_date_path(date_obj):
    """
    Generate path format based on date object: YYYY/MM/DD
    """
    return f"{date_obj.year:04d}/{date_obj.month:02d}/{date_obj.day:02d}"

def get_target_filename(original_name, mapped_name, date_obj):
    """
    Generate target filename format: original_name_mapped_name_YYYY_MMDD.txt
    """
    date_str = f"{date_obj.year:04d}_{date_obj.month:02d}{date_obj.day:02d}"
    return f"{original_name}_{mapped_name}_{date_str}.txt"

def find_and_copy_files(target_date=None):
    """
    Find and copy files

    Args:
        target_date: Target date, defaults to yesterday
    """
    if target_date is None:
        # Default to process yesterday's logs
        target_date = datetime.now() - timedelta(days=1)

    logging.info(f"Starting to process date: {target_date.strftime('%Y-%m-%d')}")

    # Ensure target directory exists
    target_dir = Path(TARGET_DIR)
    target_dir.mkdir(parents=True, exist_ok=True)
    logging.info(f"Target directory: {target_dir}")

    date_path = get_date_path(target_date)
    copied_files = 0

    for base_dir in BASE_DIRS:
        # 构建完整的搜索路径
        search_dir = Path(base_dir) / date_path / "FSV"

        if not search_dir.exists():
            logging.warning(f"目录不存在: {search_dir}")
            continue

        logging.info(f"搜索目录: {search_dir}")

        # 遍历文件映射
        for file_prefix, mapped_name in FILE_MAPPING.items():
            source_file = search_dir / f"{file_prefix}.txt"

            if source_file.exists():
                # 生成目标文件名
                target_filename = get_target_filename(file_prefix, mapped_name, target_date)
                target_path = target_dir / target_filename

                try:
                    # 复制文件到log_mount目录
                    shutil.copy2(source_file, target_path)
                    logging.info(f"成功复制: {source_file} -> {target_path}")
                    copied_files += 1

                except Exception as e:
                    logging.error(f"复制文件失败: {source_file} -> {target_path}, 错误: {e}")
            else:
                logging.warning(f"文件不存在: {source_file}")

    logging.info(f"处理完成，共复制 {copied_files} 个文件")
    return copied_files

def main():
    """
    主函数
    """
    try:
        logging.info("=" * 50)
        logging.info("日志处理脚本开始执行")
        
        # 执行文件复制
        copied_count = find_and_copy_files()
        
        if copied_count > 0:
            logging.info(f"任务执行成功，共处理 {copied_count} 个文件")
        else:
            logging.warning("没有找到任何文件进行处理")
            
    except Exception as e:
        logging.error(f"脚本执行出错: {e}")
        raise
    finally:
        logging.info("日志处理脚本执行结束")
        logging.info("=" * 50)

if __name__ == "__main__":
    main()
