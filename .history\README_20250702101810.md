# 日志文件处理脚本

## 功能描述

这个Python脚本用于每天自动处理日志文件，从指定的源目录复制文件到目标位置并重命名。

## 文件说明

- `daily_log_processor.py` - 主要的Python处理脚本
- `setup_cron.sh` - 设置定时任务的Shell脚本
- `README.md` - 使用说明文档

## 脚本功能

1. **自动执行时间**: 每天凌晨2点
2. **处理日期**: 默认处理前一天的日志文件
3. **源目录**: 
   - `/home/<USER>/psis_console_log/log_mount_137/YYYY/MM/DD/FSV`
   - `/home/<USER>/psis_console_log/log_mount_196/YYYY/MM/DD/FSV`
4. **文件映射**:
   ```
   162.11_P5 -> 100.120.162.113_slot11
   162.12_P9 -> 100.120.162.113_slot12
   162.19_P1 -> 100.120.162.106_slot11
   ```
5. **目标文件名格式**: `原名_映射名_YYYY_MMDD.txt`

## 安装和配置

### 1. 部署脚本文件

将所有脚本文件放置到服务器的合适目录，例如：
```bash
mkdir -p /opt/log_processor
cd /opt/log_processor
# 将脚本文件复制到此目录
```

### 2. 设置权限

```bash
chmod +x daily_log_processor.py
chmod +x setup_cron.sh
```

### 3. 安装定时任务

```bash
./setup_cron.sh
```

### 4. 验证定时任务

```bash
crontab -l
```

应该看到类似以下的输出：
```
0 2 * * * /usr/bin/python3 /opt/log_processor/daily_log_processor.py >> /opt/log_processor/cron.log 2>&1
```

## 手动测试

### 测试脚本功能
```bash
python3 daily_log_processor.py
```

### 测试特定日期
可以修改脚本中的 `main()` 函数来测试特定日期：
```python
# 测试2025年7月1日的日志
test_date = datetime(2025, 7, 1)
find_and_copy_files(test_date)
```

## 日志文件

- **脚本执行日志**: `log_processor.log`
- **定时任务日志**: `cron.log`

## 目录结构示例

```
源目录结构:
/home/<USER>/psis_console_log/log_mount_137/2025/07/01/FSV/
├── 162.11_P5.txt
├── 162.12_P9.txt
└── 162.19_P1.txt

/home/<USER>/psis_console_log/log_mount_196/2025/07/01/FSV/
├── 162.11_P5.txt
├── 162.12_P9.txt
└── 162.19_P1.txt

处理后的文件 (统一存放在log_mount目录):
/home/<USER>/psis_console_log/log_mount/
├── 162.11_P5_100.120.162.113_slot11_2025_0701.txt
├── 162.12_P9_100.120.162.113_slot12_2025_0701.txt
├── 162.19_P1_100.120.162.106_slot11_2025_0701.txt
├── 162.11_P5_100.120.162.113_slot11_2025_0701.txt (来自log_mount_196)
├── 162.12_P9_100.120.162.113_slot12_2025_0701.txt (来自log_mount_196)
└── 162.19_P1_100.120.162.106_slot11_2025_0701.txt (来自log_mount_196)
```

## 故障排除

### 1. 检查定时任务状态
```bash
systemctl status cron  # Ubuntu/Debian
systemctl status crond  # CentOS/RHEL
```

### 2. 查看执行日志
```bash
tail -f /opt/log_processor/cron.log
tail -f /opt/log_processor/log_processor.log
```

### 3. 手动执行测试
```bash
cd /opt/log_processor
python3 daily_log_processor.py
```

### 4. 检查文件权限
确保脚本对源目录有读权限，对目标目录有写权限。

## 配置修改

如需修改配置，请编辑 `daily_log_processor.py` 文件中的以下变量：

- `BASE_DIRS`: 基础目录列表
- `FILE_MAPPING`: 文件名映射关系
- 执行时间可通过修改crontab来调整

## 注意事项

1. 确保Python3已安装
2. 确保有足够的磁盘空间存储复制的文件
3. 定期检查日志文件，清理过期的日志
4. 建议设置日志轮转以避免日志文件过大
