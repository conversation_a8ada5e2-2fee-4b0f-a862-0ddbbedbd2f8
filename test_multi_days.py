#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test script for multi-day log processing
"""

import os
import shutil
import logging
from datetime import datetime, timedelta
from pathlib import Path

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('test_multi_days.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)

# Test configuration
BASE_DIRS = [
    "./test_logs/log_mount_137",
    "./test_logs/log_mount_196"
]

TARGET_DIR = "./test_logs/log_mount"

FILE_MAPPING = {
    "162.11_P5": "100.120.162.113_slot11",
    "162.12_P9": "100.120.162.113_slot12", 
    "162.19_P1": "100.120.162.106_slot11"
}

def get_date_path(date_obj):
    """Generate path format based on date object: YYYY/MM/DD"""
    return f"{date_obj.year:04d}/{date_obj.month:02d}/{date_obj.day:02d}"

def get_target_filename(original_name, mapped_name, date_obj):
    """Generate target filename format: original_name_mapped_name_YYYY_MMDD.txt"""
    date_str = f"{date_obj.year:04d}_{date_obj.month:02d}{date_obj.day:02d}"
    return f"{original_name}_{mapped_name}_{date_str}.txt"

def create_test_structure(days_back=3):
    """Create test directory structure and files for multiple days"""
    for day_offset in range(days_back):
        test_date = datetime(2025, 7, 1) - timedelta(days=day_offset)
        date_path = get_date_path(test_date)
        
        for base_dir in BASE_DIRS:
            # Create directory structure
            test_dir = Path(base_dir) / date_path / "FSV"
            test_dir.mkdir(parents=True, exist_ok=True)
            
            # Create test files
            for file_prefix in FILE_MAPPING.keys():
                test_file = test_dir / f"{file_prefix}.txt"
                with open(test_file, 'w', encoding='utf-8') as f:
                    f.write(f"Test log content - {file_prefix}\n")
                    f.write(f"Date: {test_date.strftime('%Y-%m-%d')}\n")
                    f.write(f"Created at: {datetime.now()}\n")
                    f.write("This is a test file\n")
            
            logging.info(f"Created test directory: {test_dir}")

def find_and_copy_files(days_back=3):
    """Find and copy files for the specified number of days back"""
    copied_files = 0
    
    # Ensure target directory exists
    target_dir = Path(TARGET_DIR)
    target_dir.mkdir(parents=True, exist_ok=True)
    logging.info(f"Target directory: {target_dir}")
    
    # Process files for each day
    for day_offset in range(days_back):
        target_date = datetime(2025, 7, 1) - timedelta(days=day_offset)
        logging.info(f"Starting to process date: {target_date.strftime('%Y-%m-%d')} (Day {day_offset + 1} of {days_back})")
        
        date_path = get_date_path(target_date)
        daily_copied_files = 0

        for base_dir in BASE_DIRS:
            # Build complete search path
            search_dir = Path(base_dir) / date_path / "FSV"
            
            if not search_dir.exists():
                logging.warning(f"Directory does not exist: {search_dir}")
                continue
                
            logging.info(f"Searching directory: {search_dir}")
            
            # Iterate through file mapping
            for file_prefix, mapped_name in FILE_MAPPING.items():
                source_file = search_dir / f"{file_prefix}.txt"
                
                if source_file.exists():
                    # Generate target filename
                    target_filename = get_target_filename(file_prefix, mapped_name, target_date)
                    target_path = target_dir / target_filename
                    
                    try:
                        # Copy file to log_mount directory
                        shutil.copy2(source_file, target_path)
                        logging.info(f"Successfully copied: {source_file} -> {target_path}")
                        copied_files += 1
                        daily_copied_files += 1
                        
                    except Exception as e:
                        logging.error(f"Failed to copy file: {source_file} -> {target_path}, Error: {e}")
                else:
                    logging.warning(f"File does not exist: {source_file}")
        
        logging.info(f"Date {target_date.strftime('%Y-%m-%d')} processing completed, copied {daily_copied_files} files")
    
    logging.info(f"All processing completed, total copied {copied_files} files")
    return copied_files

def main():
    """Main function for testing"""
    try:
        logging.info("=" * 60)
        logging.info("Multi-day log processing test started")
        
        # Test with different number of days
        test_days = 3
        logging.info(f"Testing with {test_days} days back")
        
        # Clean up previous test
        if Path("./test_logs").exists():
            shutil.rmtree("./test_logs")
        
        # Create test environment
        logging.info("Creating test directories and files...")
        create_test_structure(days_back=test_days)
        
        # Execute file copying
        copied_count = find_and_copy_files(days_back=test_days)
        
        if copied_count > 0:
            logging.info(f"Test completed successfully, processed {copied_count} files")
            
            # Display result files
            logging.info("Generated files:")
            target_path = Path(TARGET_DIR)
            if target_path.exists():
                for file in sorted(target_path.glob("*.txt")):
                    logging.info(f"  - {file.name}")
        else:
            logging.warning("Test found no files for processing")
            
    except Exception as e:
        logging.error(f"Test execution error: {e}")
        raise
    finally:
        logging.info("Multi-day log processing test finished")
        logging.info("=" * 60)

if __name__ == "__main__":
    main()
