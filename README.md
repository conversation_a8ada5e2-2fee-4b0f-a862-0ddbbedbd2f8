# Log File Processing Script

## Feature Description

This Python script is used to automatically process log files daily, copying files from specified source directories to target locations and renaming them.

## File Description

- `daily_log_processor.py` - Main Python processing script
- `setup_cron.sh` - Shell script for setting up scheduled tasks
- `README.md` - Usage documentation

## Script Features

1. **Automatic execution time**: Daily at 2 AM
2. **Processing date**: <PERSON><PERSON><PERSON> processes previous day's log files
3. **Multi-day support**: Can process multiple days back using `--days` parameter
4. **Source directories**:
   - `/home/<USER>/psis_console_log/log_mount_137/YYYY/MM/DD/FSV`
   - `/home/<USER>/psis_console_log/log_mount_196/YYYY/MM/DD/FSV`
5. **File mapping**:
   ```
   162.11_P5 -> 100.120.162.113_slot11
   162.12_P9 -> 100.120.162.113_slot12
   162.19_P1 -> 100.120.162.106_slot11
   ```
6. **Target filename format**: `original_name_mapped_name_YYYY_MMDD.txt`
7. **Target directory**: All files are copied to `/home/<USER>/psis_console_log/log_mount/`

## Installation and Configuration

### 1. Deploy Script Files

Place all script files in an appropriate directory on the server, for example:
```bash
mkdir -p /opt/log_processor
cd /opt/log_processor
# Copy script files to this directory
```

### 2. Set Permissions

```bash
chmod +x daily_log_processor.py
chmod +x setup_cron.sh
```

### 3. Install Scheduled Task

```bash
./setup_cron.sh
```

### 4. Verify Scheduled Task

```bash
crontab -l
```

You should see output similar to:
```
0 2 * * * /usr/bin/python3 /opt/log_processor/daily_log_processor.py >> /opt/log_processor/cron.log 2>&1
```

## Usage

### Basic Usage (Process yesterday's logs)
```bash
python3 daily_log_processor.py
```

### Process Multiple Days Back
```bash
# Process last 3 days
python3 daily_log_processor.py --days 3

# Process last 7 days
python3 daily_log_processor.py --days 7
```

### Command Line Options
- `--days N`: Number of days back to process (default: 1)
- `--help`: Show help message

## Manual Testing

### Test Script Functionality
```bash
# Test with default (1 day back)
python3 daily_log_processor.py

# Test with multiple days
python3 daily_log_processor.py --days 3
```

### Test Examples
```bash
# Process yesterday's logs only
python3 daily_log_processor.py --days 1

# Process last week's logs
python3 daily_log_processor.py --days 7

# Process last month's logs (30 days)
python3 daily_log_processor.py --days 30
```

## Log Files

- **Script execution log**: `log_processor.log`
- **Scheduled task log**: `cron.log`

## 目录结构示例

```
源目录结构:
/home/<USER>/psis_console_log/log_mount_137/2025/07/01/FSV/
├── 162.11_P5.txt
├── 162.12_P9.txt
└── 162.19_P1.txt

/home/<USER>/psis_console_log/log_mount_196/2025/07/01/FSV/
├── 162.11_P5.txt
├── 162.12_P9.txt
└── 162.19_P1.txt

处理后的文件 (统一存放在log_mount目录):
/home/<USER>/psis_console_log/log_mount/
├── 162.11_P5_100.120.162.113_slot11_2025_0701.txt
├── 162.12_P9_100.120.162.113_slot12_2025_0701.txt
├── 162.19_P1_100.120.162.106_slot11_2025_0701.txt
├── 162.11_P5_100.120.162.113_slot11_2025_0701.txt (来自log_mount_196)
├── 162.12_P9_100.120.162.113_slot12_2025_0701.txt (来自log_mount_196)
└── 162.19_P1_100.120.162.106_slot11_2025_0701.txt (来自log_mount_196)
```

## 故障排除

### 1. 检查定时任务状态
```bash
systemctl status cron  # Ubuntu/Debian
systemctl status crond  # CentOS/RHEL
```

### 2. 查看执行日志
```bash
tail -f /opt/log_processor/cron.log
tail -f /opt/log_processor/log_processor.log
```

### 3. 手动执行测试
```bash
cd /opt/log_processor
python3 daily_log_processor.py
```

### 4. 检查文件权限
确保脚本对源目录有读权限，对目标目录有写权限。

## 配置修改

如需修改配置，请编辑 `daily_log_processor.py` 文件中的以下变量：

- `BASE_DIRS`: 基础目录列表
- `FILE_MAPPING`: 文件名映射关系
- 执行时间可通过修改crontab来调整

## 注意事项

1. 确保Python3已安装
2. 确保有足够的磁盘空间存储复制的文件
3. 定期检查日志文件，清理过期的日志
4. 建议设置日志轮转以避免日志文件过大
