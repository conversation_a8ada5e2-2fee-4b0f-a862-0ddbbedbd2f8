#!/bin/bash
# 设置定时任务的脚本

# 获取当前脚本所在目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PYTHON_SCRIPT="$SCRIPT_DIR/daily_log_processor.py"

echo "设置定时任务..."
echo "Python脚本路径: $PYTHON_SCRIPT"

# 检查Python脚本是否存在
if [ ! -f "$PYTHON_SCRIPT" ]; then
    echo "错误: Python脚本不存在: $PYTHON_SCRIPT"
    exit 1
fi

# 给Python脚本添加执行权限
chmod +x "$PYTHON_SCRIPT"

# 创建临时的crontab文件
TEMP_CRON=$(mktemp)

# 获取当前的crontab（如果存在）
crontab -l 2>/dev/null > "$TEMP_CRON"

# 检查是否已经存在相同的任务
if grep -q "daily_log_processor.py" "$TEMP_CRON"; then
    echo "警告: 发现已存在的日志处理任务，将替换..."
    # 删除已存在的任务
    grep -v "daily_log_processor.py" "$TEMP_CRON" > "${TEMP_CRON}.new"
    mv "${TEMP_CRON}.new" "$TEMP_CRON"
fi

# 添加新的定时任务（每天凌晨2点执行）
echo "0 2 * * * /usr/bin/python3 $PYTHON_SCRIPT >> $SCRIPT_DIR/cron.log 2>&1" >> "$TEMP_CRON"

# 安装新的crontab
crontab "$TEMP_CRON"

# 清理临时文件
rm "$TEMP_CRON"

echo "定时任务设置完成！"
echo "任务详情: 每天凌晨2点执行日志处理脚本"
echo "日志文件: $SCRIPT_DIR/cron.log"
echo ""
echo "当前的crontab任务:"
crontab -l
