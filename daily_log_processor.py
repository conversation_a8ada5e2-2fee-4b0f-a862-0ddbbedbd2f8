#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Log file processing script
Executes daily at 2 AM to copy log files from specified directories and rename them
"""

import os
import shutil
import logging
import argparse
from datetime import datetime, timedelta
from pathlib import Path

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('log_processor.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)

# Configuration parameters
BASE_DIRS = [
    "/home/<USER>/psis_console_log/log_mount_137",
    "/home/<USER>/psis_console_log/log_mount_196"
]

# Target directory - all copied files will be placed here
TARGET_DIR = "/home/<USER>/psis_console_log/log_mount"

# File name mapping
FILE_MAPPING = {
    "162.11_P5": "100.120.162.113_slot11",
    "162.12_P9": "100.120.162.113_slot12",
    "162.19_P1": "100.120.162.106_slot11"
}

def get_date_path(date_obj):
    """
    Generate path format based on date object: YYYY/MM/DD
    """
    return f"{date_obj.year:04d}/{date_obj.month:02d}/{date_obj.day:02d}"

def get_target_filename(original_name, mapped_name, date_obj):
    """
    Generate target filename format: original_name_mapped_name_YYYY_MMDD.txt
    """
    date_str = f"{date_obj.year:04d}_{date_obj.month:02d}{date_obj.day:02d}"
    return f"{original_name}_{mapped_name}_{date_str}.txt"

def find_and_copy_files(days_back=1):
    """
    Find and copy files for the specified number of days back

    Args:
        days_back: Number of days back to process, defaults to 1 (yesterday)
    """
    copied_files = 0

    # Ensure target directory exists
    target_dir = Path(TARGET_DIR)
    target_dir.mkdir(parents=True, exist_ok=True)
    logging.info(f"Target directory: {target_dir}")

    # Process files for each day
    for day_offset in range(days_back):
        target_date = datetime.now() - timedelta(days=day_offset + 1)
        logging.info(f"Starting to process date: {target_date.strftime('%Y-%m-%d')} (Day {day_offset + 1} of {days_back})")

        date_path = get_date_path(target_date)
        daily_copied_files = 0

        for base_dir in BASE_DIRS:
            # Build complete search path
            search_dir = Path(base_dir) / date_path / "FSV"

            if not search_dir.exists():
                logging.warning(f"Directory does not exist: {search_dir}")
                continue

            logging.info(f"Searching directory: {search_dir}")

            # Iterate through file mapping
            for file_prefix, mapped_name in FILE_MAPPING.items():
                source_file = search_dir / f"{file_prefix}.txt"

                if source_file.exists():
                    # Generate target filename
                    target_filename = get_target_filename(file_prefix, mapped_name, target_date)
                    target_path = target_dir / target_filename

                    try:
                        # Copy file to log_mount directory
                        shutil.copy2(source_file, target_path)
                        logging.info(f"Successfully copied: {source_file} -> {target_path}")
                        copied_files += 1
                        daily_copied_files += 1

                    except Exception as e:
                        logging.error(f"Failed to copy file: {source_file} -> {target_path}, Error: {e}")
                else:
                    logging.warning(f"File does not exist: {source_file}")

        logging.info(f"Date {target_date.strftime('%Y-%m-%d')} processing completed, copied {daily_copied_files} files")

    logging.info(f"All processing completed, total copied {copied_files} files")
    return copied_files

def parse_arguments():
    """
    Parse command line arguments
    """
    parser = argparse.ArgumentParser(description='Log file processing script')
    parser.add_argument(
        '--days',
        type=int,
        default=1,
        help='Number of days back to process (default: 1)'
    )
    return parser.parse_args()

def main():
    """
    Main function
    """
    try:
        # Parse command line arguments
        args = parse_arguments()

        logging.info("=" * 50)
        logging.info("Log processing script started")
        logging.info(f"Processing logs for the past {args.days} day(s)")

        # Execute file copying
        copied_count = find_and_copy_files(days_back=args.days)

        if copied_count > 0:
            logging.info(f"Task completed successfully, processed {copied_count} files")
        else:
            logging.warning("No files found for processing")

    except Exception as e:
        logging.error(f"Script execution error: {e}")
        raise
    finally:
        logging.info("Log processing script finished")
        logging.info("=" * 50)

if __name__ == "__main__":
    main()
