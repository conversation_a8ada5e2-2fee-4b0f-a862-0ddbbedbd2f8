#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
日志文件处理脚本
每天2点执行，从指定目录复制日志文件并重命名
"""

import os
import shutil
import logging
from datetime import datetime, timedelta
from pathlib import Path

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('log_processor.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)

# 配置参数
BASE_DIRS = [
    "/home/<USER>/psis_console_log/log_mount_137",
    "/home/<USER>/psis_console_log/log_mount_196"
]

# 文件名映射
FILE_MAPPING = {
    "162.11_P5": "100.120.162.113_slot11",
    "162.12_P9": "100.120.162.113_slot12", 
    "162.19_P1": "100.120.162.106_slot11"
}

def get_date_path(date_obj):
    """
    根据日期对象生成路径格式：YYYY/MM/DD
    """
    return f"{date_obj.year:04d}/{date_obj.month:02d}/{date_obj.day:02d}"

def get_target_filename(original_name, mapped_name, date_obj):
    """
    生成目标文件名格式：原名_映射名_YYYY_MMDD.txt
    """
    date_str = f"{date_obj.year:04d}_{date_obj.month:02d}{date_obj.day:02d}"
    return f"{original_name}_{mapped_name}_{date_str}.txt"

def find_and_copy_files(target_date=None):
    """
    查找并复制文件
    
    Args:
        target_date: 目标日期，默认为昨天
    """
    if target_date is None:
        # 默认处理昨天的日志
        target_date = datetime.now() - timedelta(days=1)
    
    logging.info(f"开始处理日期: {target_date.strftime('%Y-%m-%d')}")
    
    date_path = get_date_path(target_date)
    copied_files = 0
    
    for base_dir in BASE_DIRS:
        # 构建完整的搜索路径
        search_dir = Path(base_dir) / date_path / "FSV"
        
        if not search_dir.exists():
            logging.warning(f"目录不存在: {search_dir}")
            continue
            
        logging.info(f"搜索目录: {search_dir}")
        
        # 遍历文件映射
        for file_prefix, mapped_name in FILE_MAPPING.items():
            source_file = search_dir / f"{file_prefix}.txt"
            
            if source_file.exists():
                # 生成目标文件名
                target_filename = get_target_filename(file_prefix, mapped_name, target_date)
                target_path = Path(base_dir) / target_filename
                
                try:
                    # 复制文件
                    shutil.copy2(source_file, target_path)
                    logging.info(f"成功复制: {source_file} -> {target_path}")
                    copied_files += 1
                    
                except Exception as e:
                    logging.error(f"复制文件失败: {source_file} -> {target_path}, 错误: {e}")
            else:
                logging.warning(f"文件不存在: {source_file}")
    
    logging.info(f"处理完成，共复制 {copied_files} 个文件")
    return copied_files

def main():
    """
    主函数
    """
    try:
        logging.info("=" * 50)
        logging.info("日志处理脚本开始执行")
        
        # 执行文件复制
        copied_count = find_and_copy_files()
        
        if copied_count > 0:
            logging.info(f"任务执行成功，共处理 {copied_count} 个文件")
        else:
            logging.warning("没有找到任何文件进行处理")
            
    except Exception as e:
        logging.error(f"脚本执行出错: {e}")
        raise
    finally:
        logging.info("日志处理脚本执行结束")
        logging.info("=" * 50)

if __name__ == "__main__":
    main()
