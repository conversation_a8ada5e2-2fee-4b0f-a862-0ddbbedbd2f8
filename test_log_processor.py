#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
日志文件处理脚本 - 测试版本
用于在Windows环境下测试功能
"""

import os
import shutil
import logging
from datetime import datetime, timedelta
from pathlib import Path

# 配置日志 - 针对Windows环境优化
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('log_processor.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)

# 测试用的配置参数 - 使用当前目录进行测试
BASE_DIRS = [
    "./test_logs/log_mount_137",
    "./test_logs/log_mount_196"
]

# 目标目录 - 所有复制的文件都放到这里
TARGET_DIR = "./test_logs/log_mount"

# 文件名映射
FILE_MAPPING = {
    "162.11_P5": "100.120.162.113_slot11",
    "162.12_P9": "100.120.162.113_slot12", 
    "162.19_P1": "100.120.162.106_slot11"
}

def create_test_structure(days_back=3):
    """
    Create test directory structure and files for multiple days
    """
    for day_offset in range(days_back):
        test_date = datetime(2025, 7, 1) - timedelta(days=day_offset)
        date_path = f"{test_date.year:04d}/{test_date.month:02d}/{test_date.day:02d}"

        for base_dir in BASE_DIRS:
            # Create directory structure
            test_dir = Path(base_dir) / date_path / "FSV"
            test_dir.mkdir(parents=True, exist_ok=True)

            # Create test files
            for file_prefix in FILE_MAPPING.keys():
                test_file = test_dir / f"{file_prefix}.txt"
                with open(test_file, 'w', encoding='utf-8') as f:
                    f.write(f"Test log content - {file_prefix}\n")
                    f.write(f"Date: {test_date.strftime('%Y-%m-%d')}\n")
                    f.write(f"Created at: {datetime.now()}\n")
                    f.write("This is a test file\n")

            logging.info(f"Created test directory: {test_dir}")

def get_date_path(date_obj):
    """
    根据日期对象生成路径格式：YYYY/MM/DD
    """
    return f"{date_obj.year:04d}/{date_obj.month:02d}/{date_obj.day:02d}"

def get_target_filename(original_name, mapped_name, date_obj):
    """
    生成目标文件名格式：原名_映射名_YYYY_MMDD.txt
    """
    date_str = f"{date_obj.year:04d}_{date_obj.month:02d}{date_obj.day:02d}"
    return f"{original_name}_{mapped_name}_{date_str}.txt"

def find_and_copy_files(target_date=None):
    """
    查找并复制文件

    Args:
        target_date: 目标日期，默认为指定测试日期
    """
    if target_date is None:
        # 使用测试日期
        target_date = datetime(2025, 7, 1)

    logging.info(f"开始处理日期: {target_date.strftime('%Y-%m-%d')}")

    # 确保目标目录存在
    target_dir = Path(TARGET_DIR)
    target_dir.mkdir(parents=True, exist_ok=True)
    logging.info(f"目标目录: {target_dir}")

    date_path = get_date_path(target_date)
    copied_files = 0

    for base_dir in BASE_DIRS:
        # 构建完整的搜索路径
        search_dir = Path(base_dir) / date_path / "FSV"

        if not search_dir.exists():
            logging.warning(f"目录不存在: {search_dir}")
            continue

        logging.info(f"搜索目录: {search_dir}")

        # 遍历文件映射
        for file_prefix, mapped_name in FILE_MAPPING.items():
            source_file = search_dir / f"{file_prefix}.txt"

            if source_file.exists():
                # 生成目标文件名
                target_filename = get_target_filename(file_prefix, mapped_name, target_date)
                target_path = target_dir / target_filename

                try:
                    # 复制文件到log_mount目录
                    shutil.copy2(source_file, target_path)
                    logging.info(f"成功复制: {source_file} -> {target_path}")
                    copied_files += 1

                except Exception as e:
                    logging.error(f"复制文件失败: {source_file} -> {target_path}, 错误: {e}")
            else:
                logging.warning(f"文件不存在: {source_file}")

    logging.info(f"处理完成，共复制 {copied_files} 个文件")
    return copied_files

def main():
    """
    主函数 - 测试版本
    """
    try:
        logging.info("=" * 50)
        logging.info("日志处理脚本测试开始")
        
        # 创建测试环境
        logging.info("创建测试目录和文件...")
        create_test_structure()
        
        # 执行文件复制
        copied_count = find_and_copy_files()
        
        if copied_count > 0:
            logging.info(f"测试成功，共处理 {copied_count} 个文件")

            # 显示结果文件
            logging.info("生成的文件:")
            target_path = Path(TARGET_DIR)
            if target_path.exists():
                for file in target_path.glob("*.txt"):
                    logging.info(f"  - {file}")
        else:
            logging.warning("测试中没有找到任何文件进行处理")
            
    except Exception as e:
        logging.error(f"脚本执行出错: {e}")
        raise
    finally:
        logging.info("日志处理脚本测试结束")
        logging.info("=" * 50)

if __name__ == "__main__":
    main()
